{"name": "@paris2/risk-assessment", "scripts": {"start": "webpack serve --port 9011 --https", "start:standalone": "webpack serve --env standalone ", "build": "concurrently npm:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint --fix src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "test": "jest --verbose --coverage --testResultsProcessor jest-sonar-reporter", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "prepare": "husky install", "coverage": "cross-env BABEL_ENV=test jest --coverage", "build:types": "tsc", "lint-staged": "lint-staged", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx,scss,less}\"", "eslint": "eslint --ext \"src/**/*.{js,jsx,ts,tsx}\""}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,scss,less}": ["prettier --write", "git add"], "src/**/*.{js,jsx}": ["eslint"]}, "jestSonar": {"reportPath": "reports", "reportFile": "test-report.xml", "indent": 4, "collectCoverage": true}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "7.15.0", "@babel/plugin-transform-runtime": "7.15.0", "@babel/preset-env": "7.15.0", "@babel/preset-react": "7.14.5", "@babel/preset-typescript": "7.15.0", "@babel/runtime": "7.15.3", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/axios": "0.14.0", "@types/jest": "27.0.1", "@types/lodash": "4.17.13", "@types/mocha": "10.0.1", "@types/react": "^18.3.23", "@types/react-dom": "18.3.5", "@types/systemjs": "6.1.1", "@types/testing-library__jest-dom": "5.14.1", "@types/webpack-env": "1.16.2", "@typescript-eslint/eslint-plugin": "5.59.8", "@typescript-eslint/parser": "5.59.8", "babel-jest": "27.0.6", "commitizen": "^4.3.1", "concurrently": "^9.1.2", "css-loader": "6.7.3", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-config-ts-react-important-stuff": "3.0.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-react": "7.32.2", "file-loader": "^6.2.0", "husky": "^9.1.7", "jest": "27.0.6", "jest-cli": "27.0.6", "prettier": "2.3.2", "sass": "1.60.0", "sass-loader": "13.2.2", "standalone-single-spa-webpack-plugin": "4.0.0", "style-loader": "3.3.2", "ts-config-single-spa": "3.0.0", "typescript": "5.0.4", "webpack": "5.97.1", "webpack-cli": "4.10.0", "webpack-config-single-spa-react": "4.0.0", "webpack-config-single-spa-react-ts": "4.0.0", "webpack-config-single-spa-ts": "4.0.0", "webpack-dev-server": "4.0.0", "webpack-merge": "5.8.0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/react-table": "^8.21.3", "axios": "1.7.9", "cz-customizable": "7.0.0", "dotenv": "16.0.3", "jest-sonar-reporter": "2.0.0", "lodash": "4.17.21", "moment": "^2.30.1", "react": "18.3.1", "react-bootstrap": "2.7.4", "react-bootstrap-icons": "^1.11.6", "react-bootstrap-typeahead": "6.3.4", "react-datepicker": "^8.4.0", "react-dom": "18.3.1", "react-icons": "^5.5.0", "react-mentions": "4.4.7", "react-router-dom": "6.10.0", "react-toastify": "9.1.3", "single-spa": "5.9.3", "single-spa-react": "6.0.2", "uuid": "^11.1.0"}, "types": "dist/paris2-risk-assessment.d.ts"}