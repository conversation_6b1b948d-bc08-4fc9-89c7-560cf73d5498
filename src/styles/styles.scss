@import 'react-toastify/dist/ReactToastify.css';
@import '~react-datepicker/dist/react-datepicker.css';

$primary: #052bc2;
$secondary: #1c1f4a;

.search-input {
  height: 32px;
  padding-left: 35px;
}

.add-project-container {
  padding-left: 188px !important;
  padding-right: 188px !important;
}

.underline {
  text-decoration: underline !important;
}

.hull-lbl {
  font-size: 16px !important;
  color: #1f4a70 !important;
}

.fs-14 {
  font-size: 14px !important;
}

.fs-24 {
  font-size: 24px !important;
}

.fs-20 {
  font-size: 20px !important;
}

.fs-16 {
  font-size: 16px !important;
}

.fw-400 {
  font-weight: 400 !important;
}

.fw-600 {
  font-weight: 600;
}

.mw-48 {
  min-width: 48px;
}

.pr-16 {
  padding-right: 16px;
}

// bottom button component
.bottom-component {
  height: 58px;
  background-color: #f8f9fa;
  border-top: 1px solid #cccccc;
  text-align: center;
  padding-top: 10px;

  &__button {
    padding-left: 100px;
    padding-right: 100px;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.react-select__control {
  font-size: 14px;
  border-color: #ced4da;
  min-height: 38px;
}

.react-select__menu {
  font-size: 14px;
}

.react-select__placeholder {
  color: #6c757d;
}

.clear-icon {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
}

.Toastify__toast {
  color: #333333;
}

.fw-500 {
  font-weight: 500;
  color: #000000;
}

.upload-img {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-bottom: 16px;

  .upload-img-wrapper {
    width: 128px;
    height: 128px;
    background-color: #f5f6f7;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;

    .img-icon {
      padding: 12px;
      border-radius: 4px;
    }
  }
}

.sec-btn {
  background-color: #1f4a70 !important;
  border: 1px solid #1f4a70 !important;

  &:hover,
  &:focus,
  &:active {
    background-color: darken(#1f4a70, 10%) !important;
  }
}

.primary-btn {
  background-color: #0091b8 !important;
  border: 1px solid #0091b8 !important;

  &:hover,
  &:focus,
  &:active {
    background-color: darken(#0091b8, 10%) !important;
  }

  &:disabled {
    background: #efefef !important;
    border: 1px solid #efefef !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    color: #aaaaaa !important;
    cursor: not-allowed !important;
  }
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: $secondary;
  margin-top: -14px;
  padding-bottom: 16px;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.disabled-link {
  pointer-events: none;
}

.stepper-lbl {
  font-weight: 600;
  color: #000000;
}

.currency-symbol {
  background-color: #f1f1f1;
  color: #495057;
  pointer-events: none;
  border-right: none;
  margin-right: -2px;
}

.rounded-full {
  border-radius: 50%;
}

.input-symbol {
  background-color: #f1f1f1;
  color: #495057;
  pointer-events: none;
  border-left: none;
  margin-left: -2px;
  font-weight: 500;
}

.typeahead-wrapper {
  padding-right: -20px;

  .form-control {
    height: 32px !important;
  }

  // Invalid state styling for multiple selection dropdowns
  &.is-invalid {
    .rbt-input-multi {
      border-color: #dc3545 !important;
      box-shadow: 0 !important;
    }

    .rbt-input {
      border-color: #dc3545 !important;
      box-shadow: 0 !important;
    }
  }
}

.typeahead-wrapper .rbt-token {
  background: transparent;
  border: none;
  color: #495057;
  padding: 0;
}

.typeahead-wrapper .rbt-token-label {
  padding-left: 0px !important;
  font-size: 14px !important;
}

.typeahead-wrapper .rbt-input-wrapper {
  margin-right: 20px;
}

.typeahead-wrapper .rbt-input-multi input {
  caret-color: transparent;
}

.hull-form-title {
  font-size: 20px;
  font-weight: 600;
  color: $secondary;
}

.hull-header {
  align-items: center;
  padding-bottom: 10px;
  margin-top: -10px;
}

.deadweight-unit-dropdown {
  background-color: #f1f1f1;
  color: #495057;
  margin-left: -1px;
  font-weight: 500;
  padding-left: 10px;
  cursor: pointer;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border: 1px solid #ced4da;
}

.nbp-datepicker {
  .react-datepicker__close-icon {
    display: none !important;
  }

  .clear-icon {
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 2;
  }

  .react-datepicker-popper {
    z-index: 11;
  }

  input {
    height: 32px !important;
  }
}

.pre-line {
  white-space: pre-line;
}

.ra-negate-padding {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.ra-breadcrumb {
  font-weight: 400;
  font-size: 24px;
  line-height: 36px;
  color: #1f4a70;

  .header {
    all: unset !important;
    cursor: pointer !important;
    text-decoration-line: underline !important;
  }
}

.px-12-py-6 {
  padding: 12px 6px !important;
  width: 180px !important;
}

.badge-keyword {
  color: #333333 !important;
  background-color: #f5f5f5 !important;
  text-wrap: auto !important;
}

.top-modal {
  display: flex;
  align-items: flex-start;
  padding-top: 80px;
}

.secondary-color {
  color: #1f4a70 !important;
}

.primary-txt-color {
  color: #333333 !important;
}

.btn-secondary-bg {
  background: #0091b8 !important;
  color: #ffffff !important;
  border: 1px solid #0091b8 !important;
}

.sticky-styles-no-border-left {
  position: sticky !important;
  left: 0 !important;
  background: #fff !important;
}

.sticky-styles-no-border-right {
  position: sticky !important;
  left: 0 !important;
  background: #fff !important;
}

.sticky-styles-tl-action {
  position: sticky !important;
  right: 0 !important;
  width: 90px !important;
  background: #fff !important;
}

.sticky-styles-tl-comment {
  position: sticky !important;
  right: 90px !important;
  width: 90px !important;
  border-left: 1px solid #dee2e6 !important;
  background: #fff !important;
}

.h-37 {
  height: 37px !important;
}

.draft-listing-tab {
  font-size: 14px;
  border-radius: 0;
  padding: 0.5rem 1.5rem;
  border: 1px solid transparent;

  &:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  &:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &.inactive-tab {
    background-color: #fff;
    color: #1f4a70;
    border-color: #1f4a70;
  }

  &.active-tab {
    background-color: #1f4a70;
    color: #fff;
    border-color: #1f4a70;
  }
}

.ra-alert-warning {
  background-color: #faf2f5;
  color: #c82333;
}

.ra-no-style-btn {
  all: unset !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.delete-job-border {
  border: 1px solid #cccccc;
  border-radius: 4px;
  padding: 12px;
}

.edit-modal-body {
  max-height: 65vh;
  overflow-y: auto;
}

.calendar-icon {
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 2;
}

.nbp-typehead-dropdown {
  position: absolute;
  inset: 0px auto auto 0px;
  display: block;
  max-height: 300px;
  overflow: auto;
  transform: translate(0px, 36px);
  width: -webkit-fill-available;

  .dropdown-special-item {
    position: sticky;
    bottom: -10px;
    background: white;
    padding: 10px 10px 10px 24px;
    border-top: 1px solid #cccccc !important;
    cursor: pointer;
    color: #052bc2;
    text-decoration: underline;
    font-size: 16px;
    font-weight: 400;

    // Remove native button styles
    border: none;
    outline: none;
    appearance: none;
    width: 100%;
    text-align: left;

    &:hover {
      background-color: #f2f7ff !important;
    }

    &:focus-visible {
      outline: 2px solid #052bc2;
      outline-offset: 2px;
    }
  }
}

.rbt-token {
  background-color: #f1f5f9;
  border-radius: 16px;
  padding: 2px 6px;
  margin: 2px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.rbt-token-more {
  font-weight: 500;
  margin-left: 6px;
  background-color: #edf3f7;
  border-radius: 12px;
  padding: 1px 6px;
  cursor: default;
  position: absolute;
  right: 36px;
  z-index: 10;
}

.dropdown-no-results {
  padding-left: 24px !important;
}

.gap-16px {
  gap: 16px;
}

.h-60vh {
  height: 60vh;
}

.h-390p {
  height: 390px;
}

.p-16px {
  padding: 16px;
  margin-bottom: -16px;
}

.preview-template-modal {
  max-width: 1310px !important;
}

.preview-form-div {
  .form-control:disabled {
    background-color: #fff !important;
  }
}

.search-crew-bar {
  width: 390px;
}

.user-profile-info {
  display: flex !important;
  align-items: center !important;
  padding: 8px !important;
  cursor: pointer !important;
  transition: background 0.1s !important;

  &:hover {
    background: #f8fafc !important;
  }

  .avatar {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin: 0px 12px;

    width: 48px;
    height: 48px;

    background: #e5f4f8;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
    color: #1f4a70;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .user-name {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;

      color: #333333;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-details {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;

      color: #6c757d;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.gap-24p {
  gap: 24px;
}

.crew-member-profile {
  flex: 0 0 calc(40% - 0.5rem);
  background: #f3f6f8;
  border-color: #f3f6f8 !important;
}

.crew-member-profile-edit {
  flex: 0 0 calc(49% - 0.5rem);
  background: #f3f6f8;
  border-color: #f3f6f8 !important;
}

.close-out-resp-field {
  height: 32px !important;
}

.overlay-loader {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  margin-top: 0.5rem;
}

.unset {
  all: unset !important;
}

.reset-button {
  all: unset;
  cursor: pointer;
  display: block;
  width: 100%;
}

.overflowx-hidden {
  overflow-x: hidden;
}

.clear-rrr {
  display: flex !important;
  gap: 4px !important;
}

.padding-lt-16 {
  padding-top: 16px !important;
  padding-left: 16px !important;
}

.w-100 {
  width: 100% !important;
}

.clear-vessel-office {
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
  cursor: default;

  &.clickable {
    pointer-events: auto;
    cursor: pointer;
  }
}

.cursor-pointer {
  cursor: pointer !important;
}

.custom-checkbox-button {
  all: unset;
  padding: 8px 12px;
  cursor: pointer;
  display: block;
  width: 100%;

  &:focus {
    outline: none;
  }
}

.custom-risk-modal {
  max-width: 1350px !important;
  width: 100%;
  margin: auto;
}

.job-sr-no-cell {
  min-width: 65px;
  max-width: 65px;
  width: 65px;
}

.hr-rem {
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.risk-rating-pill {
  min-width: 120px;
  padding: 2px 8px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 12px;
  outline: none;
  text-align: center;
  transition: background 0.2s, color 0.2s;
}

.view-btn {
  color: #1f4a70;
  text-decoration: underline !important;
}

.w-290p {
  width: 320px;
}
