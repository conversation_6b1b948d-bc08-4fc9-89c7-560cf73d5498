import React, {
  useState,
  forwardRef,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  useEffect,
} from 'react';
import {v4 as uuidv4} from 'uuid';
import {Col, Form, Row, Button, Card, Collapse} from 'react-bootstrap';
import {InputComponent} from '../../components/InputComponent';
import {TemplateFormJob, TemplateForm} from '../../types/template';
import {RiskFormJob, RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';
import {
  DeleteJobIcon,
  JobCardArrowDownIcon,
  JobCardArrowUpIcon,
} from '../../utils/svgIcons';
import InitialRiskRatingModal, {
  consequenceRows,
  getCellColor,
  likelihoodCols,
} from '../../components/InitialRiskRatingModal';
import CustomDatePicker from '../../components/CustomDatePicker';
import {
  formatDateToYYYYMMDD,
  removeAndReindexJobState,
} from '../../utils/helper';
import {format} from 'date-fns';
import {
  initialRiskRatingField,
  residualRiskRatingField,
} from '../../constants/template';
import DropdownTypeahead from '../../components/DropdownTypeahead';
import GuidancePdf from '../../../public/GuidPdf.pdf';
import RiskMatrixPdf from '../../../public/RiskMatrixPdf.pdf';
import {X} from 'react-bootstrap-icons';
import LevelOfRATag from '../../components/LevelOfRATag';

// Utility to get label for a code like "E2"
export function getRiskLabel(code: string): string {
  if (!code) return '';
  // Example: E2 => consequence: E, likelihood: 2
  const regex = /^([A-E])([1-5])$/i;
  const match = regex.exec(code);
  if (!match) return code;
  const [, consequenceLetter, likelihoodNum] = match;
  const consequence = consequenceRows.find(row =>
    row.label.includes(`(${consequenceLetter?.toLocaleUpperCase()})`),
  );
  const likelihood = likelihoodCols[parseInt(likelihoodNum, 10) - 1];
  return `${code} - ${consequence?.label?.split('(')[0].trim() || ''}, ${
    likelihood?.label?.split('(')[0].trim() || ''
  }`;
}
function getRiskLevel(code: string) {
  // e.g. C3 => {row: 3, col: 3}
  if (!code) return {row: 0, col: 0};
  const rowMap = {A: 1, B: 2, C: 3, D: 4, E: 5};
  const regex = /^([A-E])([1-5])$/i;
  const match = regex.exec(code);
  if (!match) return {row: 0, col: 0};
  return {
    row: rowMap[match[1].toUpperCase() as keyof typeof rowMap],
    col: parseInt(match[2], 10),
  };
}
const REQUIRED_TEMPLATE_JOB_FIELDS: (keyof TemplateFormJob)[] = [
  'job_step',
  'job_hazard',
  'job_nature_of_risk',
  'job_existing_control',
  'job_additional_mitigation',
  'template_job_initial_risk_rating',
  'template_job_residual_risk_rating',
];

const REQUIRED_RISK_JOB_FIELDS: (keyof RiskFormJob)[] = [
  'job_step',
  'job_hazard',
  'job_nature_of_risk',
  'job_existing_control',
  'job_additional_mitigation',
  'risk_job_initial_risk_rating',
  'risk_job_residual_risk_rating',
  'job_close_out_date',
  'job_close_out_responsibility_id',
];
const errorMsg = 'This is a mandatory field. Please fill to process.';

const getMainErrorKey = (type: string, modalType: 'initial' | 'residual') => {
  let mainErrorKey = '';
  if (type === 'risk') {
    if (modalType === 'residual') {
      mainErrorKey = 'risk_job_residual_risk_rating';
    } else {
      mainErrorKey = 'risk_job_initial_risk_rating';
    }
  } else if (modalType === 'residual') {
    mainErrorKey = 'template_job_residual_risk_rating';
  } else {
    mainErrorKey = 'template_job_initial_risk_rating';
  }
  return mainErrorKey;
};
// --- Helper Components ---
const RiskRatingButton: React.FC<{
  ratingObj: any;
  isDisabled: boolean;
  onClick: () => void;
  allowClearRisk?: boolean;
  onClearRisk?: () => void;
}> = ({
  ratingObj,
  isDisabled,
  onClick,
  allowClearRisk = false,
  onClearRisk,
}) => (
  <span className={allowClearRisk ? 'clear-rrr' : ''}>
    <button
      type="button"
      onClick={onClick}
      disabled={isDisabled}
      style={{
        minWidth: 120,
        display: 'inline-block',
        padding: '2px 8px',
        borderRadius: 50,
        fontWeight: 500,
        fontSize: 12,
        background: ratingObj?.rating
          ? getCellColor(ratingObj?.rating?.split?.('-')?.[0]?.trim())
          : '#F6F8FA',
        color: ratingObj ? '#222' : '#333333',
        border: ratingObj?.rating ? 'none' : '1px solid #e0e0e0',
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        outline: 'none',
        textAlign: 'center',
        transition: 'background 0.2s, color 0.2s',
      }}
    >
      {ratingObj?.rating ? getRiskLabel(ratingObj?.rating) : 'Not Selected'}
    </button>
    {allowClearRisk && ratingObj?.rating && (
      <button
        type="button"
        className="unset cursor-pointer"
        onClick={onClearRisk}
      >
        <X data-testid="cross-icon" className="secondary-color" size={20} />
      </button>
    )}
  </span>
);

const ReasonInput: React.FC<{
  param: any;
  jobIndex: number;
  ratingObj: any;
  onReasonChange: (jobIdx: number, paramId: number, reason: string) => void;
  reasonErrors: {[paramId: number]: string};
}> = ({param, jobIndex, ratingObj, onReasonChange, reasonErrors}) => (
  <div key={param?.id} className="mb-2">
    <Form.Control
      name={`reason_for_lowering_${param?.id}_${jobIndex}`}
      value={ratingObj?.reason || ''}
      onChange={e => onReasonChange(jobIndex, param?.id, e.target?.value)}
      placeholder="Enter the Reason"
      type="text"
      maxLength={255}
      style={{
        border: '1px solid #DEE2E6',
        borderRadius: 6,
        fontSize: 15,
        height: 44,
        background: '#fff',
      }}
      autoComplete="off"
      disabled={!ratingObj?.rating}
    />
    {reasonErrors[param.id] && (
      <div style={{color: '#D41B56', fontSize: 13, marginTop: 2}}>
        {reasonErrors[param.id]}
      </div>
    )}
  </div>
);

// Component for rendering initial risk rating
const InitialRiskRatingSection: React.FC<{
  riskParameters: any[];
  job: any;
  jobIndex: number;
  onRiskRatingClick: (
    jobIdx: number,
    paramId: number,
    type: 'initial' | 'residual',
  ) => void;
  onClearRiskRating: (jobIdx: number, paramId: number) => void;
  jobErrors?: {[field: string]: string};
  touched?: {[field: string]: boolean};
  type?: 'template' | 'risk';
}> = ({
  riskParameters,
  job,
  jobIndex,
  onRiskRatingClick,
  onClearRiskRating,
  jobErrors = {},
  touched = {},
  type = 'template',
}) => {
  return (
    <Col md={4}>
      <div style={{display: 'flex', gap: 4, flexDirection: 'column'}}>
        <span style={{fontWeight: 500, fontSize: 14}}>Initial Risk Rating</span>
        <span style={{fontWeight: 400, fontSize: 14, color: '#6C757D'}}>
          Click the cards to set the Risk Ratings
        </span>
      </div>
      <div
        className="d-flex flex-wrap gap-2 mb-2"
        style={{flexDirection: 'column'}}
      >
        {riskParameters?.map?.(param => {
          const ratingObj = job?.[initialRiskRatingField(type)]?.find?.(
            (r: any) => r?.parameter_type_id === param?.id,
          );
          return (
            <div
              key={param?.id}
              className="d-flex align-items-center mb-2"
              style={{
                gap: 12,
                height: 44,
                justifyContent: 'space-between',
                paddingTop: 12,
                paddingRight: 16,
                paddingBottom: 12,
                paddingLeft: 16,
                borderRadius: 6,
                border: '1px solid var(--border-quarternary, #DEE2E6)',
              }}
            >
              <span
                style={{
                  minWidth: 100,
                  fontWeight: 600,
                  color: '#1F4A70',
                  fontSize: 14,
                }}
              >
                {param?.name}:
              </span>
              <RiskRatingButton
                ratingObj={ratingObj}
                isDisabled={false}
                onClick={() =>
                  onRiskRatingClick(jobIndex, param?.id, 'initial')
                }
                allowClearRisk
                onClearRisk={() => onClearRiskRating(jobIndex, param?.id)}
              />
            </div>
          );
        })}
        {(() => {
          return (
            jobErrors?.[initialRiskRatingField(type)] &&
            touched?.[initialRiskRatingField(type)] && (
              <div style={{color: '#D41B56', fontSize: 13, marginTop: 2}}>
                {jobErrors?.[initialRiskRatingField(type)]}
              </div>
            )
          );
        })()}
      </div>
    </Col>
  );
};

/** Helper to calculate reason errors for a job */
function getReasonErrorsForJob(
  riskParameters: any[],
  job: any,
  initialRiskRatingFieldName: string,
  residualRiskRatingFieldName: string,
): {[paramId: number]: string} {
  const errors: {[paramId: number]: string} = {};
  riskParameters?.forEach(param => {
    const irrObj = job?.[initialRiskRatingFieldName]?.find?.(
      (r: any) => r?.parameter_type_id === param?.id,
    );
    const rrrObj = job?.[residualRiskRatingFieldName]?.find?.(
      (r: any) => r?.parameter_type_id === param?.id,
    );
    const irr = irrObj?.rating;
    const rrr = rrrObj?.rating;
    const reason = rrrObj?.reason || '';
    if (irr && rrr) {
      const irrLevel = getRiskLevel(irr);
      const rrrLevel = getRiskLevel(rrr);
      const levelDiff = irrLevel.row - rrrLevel.row;
      const colDiff = irrLevel.col - rrrLevel.col;
      if (
        levelDiff > 2 ||
        (levelDiff === 0 && colDiff > 2) ||
        irrLevel.row !== rrrLevel.row
      ) {
        if (!reason.trim()) {
          errors[param.id] =
            'Risk is significantly reduced. Providing the reason is mandatory.';
        }
      }
    }
  });
  return errors;
}

/** ResidualRiskRatingSection - refactored for lower complexity */
const ResidualRiskRatingSection: React.FC<{
  riskParameters: any[];
  job: any;
  jobIndex: number;
  onRiskRatingClick: (
    jobIdx: number,
    paramId: number,
    type: 'initial' | 'residual',
  ) => void;
  onReasonChange: (jobIdx: number, paramId: number, reason: string) => void;
  jobErrors?: {[field: string]: string};
  touched?: {[field: string]: boolean};
  reasonErrors: {[paramId: number]: string};
  setReasonErrors: (errObj: {[paramId: number]: string}) => void;
  type?: 'template' | 'risk';
}> = ({
  riskParameters,
  job,
  jobIndex,
  onRiskRatingClick,
  onReasonChange,
  jobErrors = {},
  reasonErrors,
  setReasonErrors,
  touched,
  type = 'template',
}) => {
  const initialRiskRatingFieldName = initialRiskRatingField(type);
  const residualRiskRatingFieldName = residualRiskRatingField(type);

  // Effect: calculate reason errors for this job
  React.useEffect(() => {
    setReasonErrors(
      getReasonErrorsForJob(
        riskParameters,
        job,
        initialRiskRatingFieldName,
        residualRiskRatingFieldName,
      ),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [riskParameters, job]);

  return (
    <Col md={12}>
      <Row>
        <Col md={4}>
          <div className="fs-14" style={{fontWeight: 500, marginBottom: 8}}>
            Residual Risk Rating
          </div>
          {riskParameters?.map?.(param => {
            const ratingObj = job?.[residualRiskRatingFieldName]?.find?.(
              (r: any) => r?.parameter_type_id === param?.id,
            );
            const initialRatingObj = job?.[initialRiskRatingFieldName]?.find?.(
              (r: any) => r?.parameter_type_id === param?.id,
            );
            const isDisabled = !initialRatingObj?.rating;
            const errorMsg = jobErrors[`rrr_${param.id}`];
            return (
              <div key={param?.id}>
                <div
                  className="d-flex align-items-center mb-2"
                  style={{
                    gap: 12,
                    height: 44,
                    justifyContent: 'space-between',
                    padding: '12px 16px',
                    borderRadius: 6,
                    border: '1px solid var(--border-quarternary, #DEE2E6)',
                    background: isDisabled ? '#f5f5f5' : '#fff',
                    opacity: isDisabled ? 0.6 : 1,
                    pointerEvents: isDisabled ? 'none' : 'auto',
                  }}
                >
                  <span
                    style={{
                      minWidth: 100,
                      fontWeight: 600,
                      color: '#1F4A70',
                      fontSize: 14,
                    }}
                  >
                    {param?.name}:
                  </span>
                  <RiskRatingButton
                    ratingObj={ratingObj}
                    isDisabled={isDisabled}
                    onClick={() => {
                      if (!isDisabled)
                        onRiskRatingClick(jobIndex, param?.id, 'residual');
                    }}
                  />
                </div>
                {errorMsg && (
                  <div style={{color: '#D41B56', fontSize: 13, marginTop: 2}}>
                    {errorMsg}
                  </div>
                )}
              </div>
            );
          })}
          {jobErrors?.[initialRiskRatingFieldName] &&
            touched?.[initialRiskRatingFieldName] && (
              <div style={{color: '#D41B56', fontSize: 13, marginTop: 2}}>
                {jobErrors?.[initialRiskRatingFieldName]}
              </div>
            )}
        </Col>
        <Col md={8}>
          <div className="fs-14" style={{fontWeight: 500, marginBottom: 8}}>
            Reason for Lowering
          </div>
          {riskParameters?.map?.(param => {
            const ratingObj = job?.[residualRiskRatingFieldName]?.find?.(
              (r: any) => r?.parameter_type_id === param?.id,
            );
            return (
              <ReasonInput
                key={param?.id}
                param={param}
                jobIndex={jobIndex}
                ratingObj={ratingObj}
                onReasonChange={onReasonChange}
                reasonErrors={reasonErrors}
              />
            );
          })}
        </Col>
      </Row>
    </Col>
  );
};

// Component for job form fields
const JobFormFields: React.FC<{
  job: TemplateFormJob | RiskFormJob;
  jobIndex: number;
  onJobChange: (idx: number, field: string, value: any) => void;
  jobErrors?: {[field: string]: string};
  touched?: {[field: string]: boolean};
  setTouched?: any;
}> = ({
  job,
  jobIndex,
  onJobChange,
  jobErrors = {},
  touched = {},
  setTouched,
}) => {
  const setTouchedField = (field: string) => {
    setTouched((prev: {[jobIdx: number]: {[field: string]: boolean}}) => ({
      ...prev,
      [jobIndex]: {...(prev[jobIndex] || {}), [field]: true},
    }));
  };

  return (
    <>
      <Row className="mb-2">
        <Col md={6}>
          <InputComponent
            label="Job Step"
            name="job_step"
            value={job?.job_step}
            onChange={(
              e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
            ) => onJobChange(jobIndex, 'job_step', e.target?.value)}
            onBlur={() => setTouchedField('job_step')}
            placeholder="Enter Job Step"
            type="text"
            formControlId={`job_step_${jobIndex}`}
            maxLength={255}
            form={job}
            error={
              jobErrors.job_step && touched?.job_step
                ? jobErrors.job_step
                : undefined
            }
          />
        </Col>
        <Col md={6}>
          <InputComponent
            label="Hazard"
            name="job_hazard"
            value={job?.job_hazard}
            onChange={(
              e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
            ) => onJobChange(jobIndex, 'job_hazard', e.target?.value)}
            placeholder="Enter Hazard"
            type="text"
            formControlId={`job_hazard_${jobIndex}`}
            onBlur={() => setTouchedField('job_hazard')}
            maxLength={255}
            form={job}
            helpText="Best Practice is to add only one Hazard per Job Step"
            error={
              jobErrors.job_hazard && touched?.job_hazard
                ? jobErrors.job_hazard
                : undefined
            }
          />
        </Col>
      </Row>
      <Row className="mb-2">
        <Col md={6}>
          <InputComponent
            label="Nature of Risk"
            name="job_nature_of_risk"
            value={job?.job_nature_of_risk}
            onChange={(
              e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
            ) => onJobChange(jobIndex, 'job_nature_of_risk', e.target?.value)}
            onBlur={() => setTouchedField('job_nature_of_risk')}
            placeholder="e.g. Fatality, Pollution, Structural Damage, Harm to Reputation"
            type="textarea"
            formControlId={`job_nature_of_risk_${jobIndex}`}
            maxLength={255}
            showMaxLength
            rows={3}
            form={job}
            error={
              jobErrors.job_nature_of_risk && touched?.job_nature_of_risk
                ? jobErrors.job_nature_of_risk
                : undefined
            }
          />
        </Col>
        <Col md={6}>
          <InputComponent
            label="Existing Control"
            name="job_existing_control"
            value={job?.job_existing_control}
            onChange={(
              e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
            ) => onJobChange(jobIndex, 'job_existing_control', e.target?.value)}
            placeholder="List existing controls"
            type="textarea"
            onBlur={() => setTouchedField('job_existing_control')}
            formControlId={`job_existing_control_${jobIndex}`}
            maxLength={4000}
            showMaxLength
            rows={3}
            form={job}
            error={
              jobErrors.job_existing_control && touched?.job_existing_control
                ? jobErrors.job_existing_control
                : undefined
            }
          />
        </Col>
      </Row>
    </>
  );
};

// Component for close out section
const CloseOutSection: React.FC<{
  job: any;
  jobIndex: number;
  onJobChange: (idx: number, field: string, value: any) => void;
  ranks?: Array<{value: string; label: string}>;
  jobErrors?: {[field: string]: string};
  touched?: {[field: string]: boolean};
  setTouched?: any;
}> = ({
  job,
  jobIndex,
  onJobChange,
  ranks = [],
  jobErrors = {},
  touched = {},
  setTouched,
}) => {
  // Find the selected rank option
  const selectedRank =
    ranks.find(
      option =>
        option.value === job?.job_close_out_responsibility_id?.toString(),
    ) || null;

  // Handle dropdown and date blur for touched state
  const handleDropdownDateBlur = (fieldName: string) => {
    if (setTouched) {
      setTouched((prev: any) => ({
        ...prev,
        [jobIndex]: {
          ...prev[jobIndex],
          [fieldName]: true,
        },
      }));
    }
  };

  return (
    <Row>
      <Col md={6}>
        <CustomDatePicker
          label="Close Out Date"
          value={
            job?.job_close_out_date
              ? new Date(job?.job_close_out_date)
              : undefined
          }
          onChange={date =>
            onJobChange(
              jobIndex,
              'job_close_out_date',
              date ? formatDateToYYYYMMDD(date) : '',
            )
          }
          onBlur={() => handleDropdownDateBlur('job_close_out_date')}
          placeholder="Select Date"
          controlId={`job_close_out_date_${jobIndex}`}
          isRequired={true}
          isInvalid={
            touched?.job_close_out_date && !!jobErrors?.job_close_out_date
          }
          errorMsg={
            touched?.job_close_out_date
              ? jobErrors?.job_close_out_date || ''
              : ''
          }
          minDate={undefined}
        />
      </Col>
      <Col md={6}>
        <DropdownTypeahead
          label="Close Out Responsibility"
          options={ranks}
          selected={selectedRank}
          onChange={(value: any) => {
            onJobChange(
              jobIndex,
              'job_close_out_responsibility_id',
              value?.value || '',
            );
            onJobChange(
              jobIndex,
              'job_close_out_responsibility_label',
              value?.label || '',
            );
          }}
          onInputChange={() =>
            handleDropdownDateBlur('job_close_out_responsibility_id')
          }
          onBlur={() =>
            handleDropdownDateBlur('job_close_out_responsibility_id')
          }
          isInvalid={
            touched?.job_close_out_responsibility_id &&
            !!jobErrors?.job_close_out_responsibility_id
          }
          errorMessage={
            touched?.job_close_out_responsibility_id
              ? jobErrors?.job_close_out_responsibility_id || errorMsg
              : ''
          }
        />
      </Col>
    </Row>
  );
};

// Component for job card header
const JobCardHeader: React.FC<{
  jobIndex: number;
  job: any;
  expanded: number[];
  onToggleExpand: (idx: number) => void;
  onDeleteJob: (idx: number) => void;
}> = ({jobIndex, job, expanded, onToggleExpand, onDeleteJob}) => (
  <Card.Header
    className="d-flex justify-content-between align-items-center"
    style={{
      cursor: 'pointer',
      background: '#FFFFFF',
      flexDirection: 'row',
      height: '100%',
    }}
    onClick={() => onToggleExpand(jobIndex)}
  >
    <div className="d-flex align-items-center">
      <div className="d-flex" style={{flexDirection: 'column'}}>
        <b className="secondary-color fs-14 fw-500">Job {jobIndex + 1}</b>
        {!expanded?.includes?.(jobIndex) && job?.job_step && (
          <span className="ms-2 mt-1 fs-16 fw-600">{job?.job_step}</span>
        )}
      </div>
      {expanded?.includes?.(jobIndex) && (
        <Button
          variant="link"
          size="sm"
          onClick={e => {
            e.stopPropagation();
            onDeleteJob(jobIndex);
          }}
          style={{
            color: '#1F4A70',
            textDecoration: 'underline',
            fontSize: 14,
          }}
        >
          <DeleteJobIcon />
          Delete
        </Button>
      )}
    </div>
    <div style={{padding: '8px 4px', display: 'flex', alignItems: 'center'}}>
      <span style={{marginLeft: 8}}>
        {expanded?.includes?.(jobIndex) ? (
          <JobCardArrowUpIcon />
        ) : (
          <JobCardArrowDownIcon />
        )}
      </span>
    </div>
  </Card.Header>
);

// Helper function to handle risk rating updates
const updateRiskRating = (
  form: TemplateForm | RiskForm,
  jobIndex: number,
  ratingType: 'initial' | 'residual',
  parameterTypeId: number,
  value: string,
  handleJobChange: (idx: number, field: string, value: any) => void,
  type: 'template' | 'risk' = 'template',
) => {
  let fieldName = '';
  if (type === 'risk') {
    fieldName =
      ratingType === 'initial'
        ? 'risk_job_initial_risk_rating'
        : 'risk_job_residual_risk_rating';
  } else {
    fieldName =
      ratingType === 'initial'
        ? 'template_job_initial_risk_rating'
        : 'template_job_residual_risk_rating';
  }

  const jobsArray =
    type === 'risk'
      ? (form as RiskForm)?.risk_job
      : (form as TemplateForm)?.template_job;

  const prevArr = (jobsArray?.[jobIndex] as any)?.[fieldName] || [];
  const existingIndex = prevArr?.findIndex?.(
    (r: any) => r?.parameter_type_id === parameterTypeId,
  );

  let newArr;
  if (existingIndex !== -1) {
    newArr = prevArr?.map?.((r: any, i: number) =>
      i === existingIndex ? {...r, rating: value} : r,
    );
  } else {
    newArr = [
      ...(prevArr || []),
      {parameter_type_id: parameterTypeId, rating: value},
    ];
  }

  handleJobChange(jobIndex, fieldName, newArr);
};

type HandleModalSelectionOptions = {
  modalType: 'initial' | 'residual';
  form: TemplateForm | RiskForm;
  handleJobChange: (idx: number, field: string, value: any) => void;
  setJobErrors: (fn: (prev: any) => any) => void;
  validateAndNotify: (updatedForm?: TemplateForm | RiskForm) => boolean;
  setTouched: React.Dispatch<
    React.SetStateAction<{[jobIdx: number]: {[field: string]: boolean}}>
  >;
  type?: 'template' | 'risk';
};

// Helper to update residual rating array
const updateResidualRatings = (
  job: any,
  residualFieldName: string,
  selectedRiskCategory: number,
  value: string,
  handleJobChange: (idx: number, field: string, value: any) => void,
  selectedJobIdx: number,
) => {
  const residualRatings = job?.[residualFieldName] || [];
  let found = false;
  const updatedResidualRatings = residualRatings.map((r: any) => {
    if (r?.parameter_type_id === selectedRiskCategory) {
      found = true;
      return {...r, rating: value, reason: ''};
    }
    return r;
  });
  if (!found) {
    updatedResidualRatings.push({
      parameter_type_id: selectedRiskCategory,
      rating: value,
      reason: '',
    });
  }
  handleJobChange(selectedJobIdx, residualFieldName, updatedResidualRatings);
};

// Helper function to handle modal selection logic (refactored for lower cognitive complexity)
const handleModalSelection = (
  value: string,
  selectedJobIdx: number | null,
  selectedRiskCategory: number | null,
  {
    modalType,
    form,
    handleJobChange,
    setJobErrors,
    validateAndNotify,
    setTouched,
    type = 'template',
  }: HandleModalSelectionOptions,
) => {
  if (selectedJobIdx === null || selectedRiskCategory === null) return;

  // Mark IRR and RRR as touched
  setTouched((prev: {[jobIdx: number]: {[field: string]: boolean}}) => ({
    ...prev,
    [selectedJobIdx]: {
      ...(prev[selectedJobIdx] || {}),
      [initialRiskRatingField(type)]: true,
      [residualRiskRatingField(type)]: true,
    },
  }));

  if (modalType === 'initial') {
    updateRiskRating(
      form,
      selectedJobIdx,
      'initial',
      selectedRiskCategory,
      value,
      handleJobChange,
      type,
    );
    const jobsArray =
      type === 'risk'
        ? (form as RiskForm)?.risk_job
        : (form as TemplateForm)?.template_job;
    const job = jobsArray?.[selectedJobIdx];
    if (job) {
      updateResidualRatings(
        job,
        residualRiskRatingField(type),
        selectedRiskCategory,
        value,
        handleJobChange,
        selectedJobIdx,
      );
    }
  } else if (modalType === 'residual') {
    updateRiskRating(
      form,
      selectedJobIdx,
      'residual',
      selectedRiskCategory,
      value,
      handleJobChange,
      type,
    );
  }
  // Clear error for the main rating field
  const mainerrorKey = getMainErrorKey(type, modalType);

  setJobErrors(prev => {
    if (prev?.[selectedJobIdx]?.[mainerrorKey]) {
      return {
        ...prev,
        [selectedJobIdx]: {
          ...prev[selectedJobIdx],
          [mainerrorKey]: '',
        },
      };
    }
    return prev;
  });

  validateAndNotify();
};

// Helper function to get selected value for modal
const getSelectedModalValue = (
  selectedJobIdx: number | null,
  selectedRiskCategory: number | null,
  modalType: 'initial' | 'residual',
  form: TemplateForm | RiskForm,
  type: 'template' | 'risk' = 'template',
): string => {
  if (selectedJobIdx === null || selectedRiskCategory === null) {
    return '';
  }

  const jobsArray =
    type === 'risk'
      ? (form as RiskForm)?.risk_job
      : (form as TemplateForm)?.template_job;

  const job = jobsArray?.[selectedJobIdx];
  if (!job) {
    return '';
  }

  let ratingArray;
  if (type === 'risk') {
    ratingArray =
      modalType === 'residual'
        ? (job as RiskFormJob).risk_job_residual_risk_rating
        : (job as RiskFormJob).risk_job_initial_risk_rating;
  } else {
    ratingArray =
      modalType === 'residual'
        ? (job as TemplateFormJob).template_job_residual_risk_rating
        : (job as TemplateFormJob).template_job_initial_risk_rating;
  }

  const ratingObj = ratingArray?.find?.(
    (r: any) => r?.parameter_type_id === selectedRiskCategory,
  );

  return ratingObj?.rating || '';
};

// Helper function to expand all job cards
const expandAllJobs = (
  jobCount: number,
  setExpanded: React.Dispatch<React.SetStateAction<number[]>>,
) => {
  setExpanded(Array.from({length: jobCount}, (_, i) => i));
};

// Helper function to handle reason change for residual risk rating
const updateReasonForRiskRating = (
  form: TemplateForm | RiskForm,
  jobIdx: number,
  paramId: number,
  reason: string,
  handleJobChange: (idx: number, field: string, value: any) => void,
  type: 'template' | 'risk' = 'template',
) => {
  const fieldName = residualRiskRatingField(type);
  const jobsArray =
    type === 'risk'
      ? (form as RiskForm)?.risk_job
      : (form as TemplateForm)?.template_job;

  const prevArr = (jobsArray?.[jobIdx] as any)?.[fieldName] || [];
  const existingIndex = prevArr?.findIndex?.(
    (r: any) => r?.parameter_type_id === paramId,
  );

  let newArr;
  if (existingIndex !== -1) {
    newArr = prevArr?.map?.((r: any, i: number) =>
      i === existingIndex ? {...r, reason} : r,
    );
  } else {
    newArr = [
      ...(prevArr || []),
      {parameter_type_id: paramId, rating: '', reason},
    ];
  }
  handleJobChange(jobIdx, fieldName, newArr);
};

// --- Main AddJobsStep Component ---
export const AddJobsStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
      jobIndex = 0,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
      jobIndex?: number;
      type?: 'template' | 'risk';
    },
    ref,
  ) => {
    const {
      dataStore: {riskParameterListForRiskRaiting},
    } = useDataStoreContext();
    const sortedRiskParameterListForRiskRaiting =
      riskParameterListForRiskRaiting?.sort((a, b) => a.id - b.id) ?? [];
    const [showModal, setShowModal] = useState(false);
    const jobsArray =
      type === 'risk'
        ? (form as RiskForm)?.risk_job
        : (form as TemplateForm)?.template_job;

    const [expanded, setExpanded] = useState<number[]>(
      jobsArray?.length ? [0] : [],
    );
    const [selectedJobIdx, setSelectedJobIdx] = useState<number | null>(null);
    const [selectedRiskCategory, setSelectedRiskCategory] = useState<
      number | null
    >(null);
    const [modalType, setModalType] = useState<'initial' | 'residual'>(
      'initial',
    );
    const [jobErrors, setJobErrors] = useState<{
      [jobIdx: number]: {[field: string]: string};
    }>({});
    const [reasonErrors, setReasonErrors] = useState<{
      [jobIdx: number]: {[paramId: number]: string};
    }>({});
    const [touched, setTouched] = useState<{
      [jobIdx: number]: {[field: string]: boolean};
    }>({});
    const [ranks, setRanks] = useState<Array<{value: string; label: string}>>(
      [],
    );
    // Validation function
    const validate = () => {
      const errors: {[jobIdx: number]: {[field: string]: string}} = {};
      let isValid = true;

      if (!jobsArray || jobsArray.length === 0) {
        setJobErrors({0: {job_step: errorMsg}});
        if (onValidate) onValidate(false);
        return false;
      }

      jobsArray.forEach((job: TemplateFormJob | RiskFormJob, idx: number) => {
        const jobErr: {[field: string]: string} = {};
        // Check required fields based on type
        const requiredFields =
          type === 'risk'
            ? REQUIRED_RISK_JOB_FIELDS
            : REQUIRED_TEMPLATE_JOB_FIELDS;
        requiredFields.forEach(field => {
          const value = (job as any)[field];
          if (
            !value ||
            (typeof value === 'string' && !value.toString().trim()) ||
            (Array.isArray(value) && !value.length)
          ) {
            jobErr[field as string] = errorMsg;
            isValid = false;
          }
        });
        // Check IRR and RRR for each parameter
        const residualRatings =
          type === 'risk'
            ? (job as RiskFormJob).risk_job_residual_risk_rating
            : (job as TemplateFormJob).template_job_residual_risk_rating;

        if (residualRatings?.length) {
          // Check if there are any reason errors for this job
          if (
            reasonErrors?.[idx] &&
            Object.keys(reasonErrors[idx]).length > 0
          ) {
            isValid = false;
          }
        }
        if (Object.keys(jobErr).length > 0) errors[idx] = jobErr;
      });
      setJobErrors(errors);
      if (onValidate) onValidate(isValid);
      return isValid;
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    const toggleExpand = (idx: number) => {
      setExpanded(prev =>
        prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx],
      );
    };
    const validateAndNotify = () => {
      // Use updated form if provided, else current form
      const valid = validate();
      if (onValidate) onValidate(valid);
      return valid;
    };
    React.useEffect(() => {
      validateAndNotify();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [jobsArray, form.parameters, reasonErrors]);

    useEffect(() => {
      const loadSeafarerRanks = async () => {
        try {
          const ranks =
            (form as RiskForm)?.risk_team_member?.map?.(member => ({
              value: member.keycloak_id
                ? member.keycloak_id
                : member.seafarer_rank_id.toString(),
              label: member.user_name ? member.user_name : member.seafarer_rank,
            })) ?? [];

          const uniqueRanks = Array.from(
            new Map(ranks.map(rank => [rank.value, rank])).values(),
          );

          setRanks(uniqueRanks);
        } catch (error) {
          console.error('Error loading ranks:', error);
        }
      };

      if (type === 'risk') loadSeafarerRanks();
    }, []);
    const handleAddJob = async () => {
      // Mark all required fields of the new job as touched

      const createNewJob = () => {
        if (type === 'risk') {
          setForm((prev: RiskForm) => {
            const newJobs = [
              ...prev.risk_job,
              {
                job_step: '',
                job_hazard: '',
                job_nature_of_risk: '',
                job_existing_control: '',
                job_additional_mitigation: '',
                job_close_out_date: '',
                job_close_out_responsibility_id: '',
                job_close_out_responsibility_label: '',
                risk_job_initial_risk_rating: [],
                risk_job_residual_risk_rating: [],
              },
            ];
            const updatedForm = {...prev, risk_job: newJobs};
            return updatedForm;
          });
        } else {
          setForm((prev: TemplateForm) => {
            const newJobs = [
              ...prev.template_job,
              {
                job_id: uuidv4(),
                job_step: '',
                job_hazard: '',
                job_nature_of_risk: '',
                job_existing_control: '',
                job_additional_mitigation: '',
                template_job_initial_risk_rating: [],
                template_job_residual_risk_rating: [],
              },
            ];
            const updatedForm = {...prev, template_job: newJobs};
            return updatedForm;
          });
        }
      };
      if (!jobsArray?.length) return createNewJob();
      else {
        setTouched((prevTouched: any) => {
          const newTouched = {...prevTouched};
          const newJobIdx = jobsArray.length - 1;
          newTouched[newJobIdx] = newTouched[newJobIdx] || {};
          const requiredFields =
            type === 'risk'
              ? REQUIRED_RISK_JOB_FIELDS
              : REQUIRED_TEMPLATE_JOB_FIELDS;
          requiredFields.forEach(field => {
            newTouched[newJobIdx][field] = true;
          });
          return newTouched;
        });
        const valid = validate();
        if (onValidate) onValidate(valid);
        if (valid) {
          createNewJob();
          setExpanded([jobsArray.length]);
        }
      }
      // Expand only the new job (last index)
    };

    const handleJobChange = (idx: number, field: string, value: any) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => {
          const updatedJobs = [...prev.risk_job];
          updatedJobs[idx] = {...updatedJobs[idx], [field]: value};
          const updatedForm = {...prev, risk_job: updatedJobs};
          validateAndNotify();
          return updatedForm;
        });
      } else {
        setForm((prev: TemplateForm) => {
          const updatedJobs = [...prev.template_job];
          updatedJobs[idx] = {...updatedJobs[idx], [field]: value};
          const updatedForm = {...prev, template_job: updatedJobs};
          validateAndNotify();
          return updatedForm;
        });
      }
      setTouched(prev => ({
        ...prev,
        [idx]: {...(prev[idx] || {}), [field]: true},
      }));
    };

    const handleDeleteJob = (idx: number) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => {
          const updatedJobs = prev.risk_job.filter((_, i) => i !== idx);
          const updatedForm = {...prev, risk_job: updatedJobs};
          validateAndNotify();
          return updatedForm;
        });
      } else {
        setForm((prev: TemplateForm) => {
          const updatedJobs = prev.template_job.filter((_, i) => i !== idx);
          const updatedForm = {...prev, template_job: updatedJobs};
          validateAndNotify();
          return updatedForm;
        });
      }
      // Utility to remove and reindex job state objects after a job is deleted
      setTouched(prev => removeAndReindexJobState(prev, idx));
      setJobErrors(prev => removeAndReindexJobState(prev, idx));
      setReasonErrors(prev => removeAndReindexJobState(prev, idx));
    };
    const handleRiskRatingClick = (
      jobIdx: number,
      paramId: number,
      type: 'initial' | 'residual',
    ) => {
      setSelectedJobIdx(jobIdx);
      setSelectedRiskCategory(paramId);
      setModalType(type);
      setShowModal(true);
    };

    const handleReasonChange = (
      jobIdx: number,
      paramId: number,
      reason: string,
    ) => {
      updateReasonForRiskRating(
        form,
        jobIdx,
        paramId,
        reason,
        handleJobChange,
        type,
      );
    };

    const handleClearRiskRating = (jobIdx: number, paramId: number) => {
      // Clear Initial Risk Rating
      const initialFieldName = initialRiskRatingField(type);
      const residualFieldName = residualRiskRatingField(type);

      const jobsArray =
        type === 'risk'
          ? (form as RiskForm)?.risk_job
          : (form as TemplateForm)?.template_job;

      const job = jobsArray?.[jobIdx];
      if (!job) return;

      // Clear Initial Risk Rating
      const initialRatings = (job as any)?.[initialFieldName] || [];
      const updatedInitialRatings = initialRatings.filter(
        (r: any) => r?.parameter_type_id !== paramId,
      );
      handleJobChange(jobIdx, initialFieldName, updatedInitialRatings);

      // Clear Residual Risk Rating and Reason for Lowering
      const residualRatings = (job as any)?.[residualFieldName] || [];
      const updatedResidualRatings = residualRatings.filter(
        (r: any) => r?.parameter_type_id !== paramId,
      );
      handleJobChange(jobIdx, residualFieldName, updatedResidualRatings);
    };

    return (
      <div>
        {!isEdit && (
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div style={{color: '#1F4A70', fontSize: '20px', fontWeight: 600}}>
              {form?.task_requiring_ra || ''}
              {type === 'risk' && (form as RiskForm)?.date_risk_assessment && (
                <div className="d-flex align-items-center">
                  <div className="text-muted fs-14 fw-400 d-flex align-items-center">
                    Date of Risk Assessment:{' '}
                    {format(
                      new Date((form as RiskForm)?.date_risk_assessment),
                      'dd MMM yyyy',
                    )}
                  </div>
                  {'ra_level' in form && form.ra_level === 4 && (
                    <LevelOfRATag />
                  )}
                </div>
              )}
            </div>
            <div className="d-flex justify-content-between align-items-center gap-2">
              <Button
                variant="outline-primary"
                className="fs-14"
                style={{marginRight: 12}}
                onClick={() => window.open(GuidancePdf, '_blank')}
              >
                Guidance Table
              </Button>
              <Button
                variant="outline-primary"
                className="fs-14"
                style={{marginRight: 12}}
                onClick={() => window.open(RiskMatrixPdf, '_blank')}
              >
                Risk Matrix Table
              </Button>
              <Button
                className="fs-14"
                variant="primary"
                onClick={handleAddJob}
              >
                + Add Job
              </Button>
            </div>
          </div>
        )}

        {!isEdit && <hr style={{marginRight: '-1rem', marginLeft: '-1rem'}} />}
        <div>
          {!isEdit && (
            <div
              className="d-flex mb-4 mt-4"
              style={{justifyContent: 'space-between'}}
            >
              <div style={{fontSize: 16, fontWeight: 600}}>
                Hazard & Control Measures
              </div>
              {jobsArray?.length > 1 && (
                <button
                  type="button"
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: '#1F4A70',
                    textDecoration: 'underline',
                    textDecorationStyle: 'solid',
                    background: 'none',
                    border: 'none',
                    padding: 0,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    expandAllJobs(jobsArray?.length ?? 0, setExpanded);
                  }}
                >
                  Expand Job Cards
                </button>
              )}
            </div>
          )}
          {jobsArray?.map?.(
            (job: TemplateFormJob | RiskFormJob, idx: number) => (
              <Card
                key={
                  type === 'risk'
                    ? `risk-job-${idx}`
                    : (job as TemplateFormJob).job_id
                }
                className={`mb-4 ${isEdit ? 'border-0 shadow-none' : ''}`}
              >
                {!isEdit && (
                  <JobCardHeader
                    jobIndex={idx}
                    job={job}
                    expanded={expanded}
                    onToggleExpand={toggleExpand}
                    onDeleteJob={handleDeleteJob}
                  />
                )}
                <Collapse in={expanded?.includes?.(idx)}>
                  <div>
                    <Card.Body>
                      {isEdit && (
                        <Row className="mb-4">
                          <Col className="secondary-color fs-16 fw-500">
                            JOB {jobIndex + 1}
                          </Col>
                        </Row>
                      )}
                      <JobFormFields
                        job={job}
                        jobIndex={idx}
                        onJobChange={handleJobChange}
                        jobErrors={jobErrors[idx] || {}}
                        touched={touched[idx] || {}}
                        setTouched={setTouched}
                      />
                      <Row className="mb-2">
                        <InitialRiskRatingSection
                          riskParameters={sortedRiskParameterListForRiskRaiting}
                          job={job}
                          jobIndex={idx}
                          onRiskRatingClick={handleRiskRatingClick}
                          onClearRiskRating={handleClearRiskRating}
                          jobErrors={jobErrors[idx] || {}}
                          touched={touched[idx] || {}}
                          type={type}
                        />
                      </Row>
                      <Row className="mb-2">
                        <Col md={12}>
                          <InputComponent
                            label="Additional Mitigation"
                            name="job_additional_mitigation"
                            value={job?.job_additional_mitigation}
                            onChange={(
                              e: React.ChangeEvent<
                                HTMLInputElement | HTMLTextAreaElement
                              >,
                            ) =>
                              handleJobChange(
                                idx,
                                'job_additional_mitigation',
                                e.target?.value,
                              )
                            }
                            placeholder="List all the Additional Mitigation"
                            type="textarea"
                            formControlId={`job_additional_mitigation_${idx}`}
                            onBlur={() =>
                              setTouched(
                                (prev: {
                                  [jobIdx: number]: {[field: string]: boolean};
                                }) => ({
                                  ...prev,
                                  [idx]: {
                                    ...(prev[idx] || {}),
                                    job_additional_mitigation: true,
                                  },
                                }),
                              )
                            }
                            maxLength={4000}
                            showMaxLength
                            rows={3}
                            form={job}
                            error={
                              jobErrors[idx]?.job_additional_mitigation &&
                              touched[idx]?.job_additional_mitigation
                                ? jobErrors[idx].job_additional_mitigation
                                : undefined
                            }
                          />
                        </Col>
                      </Row>
                      <Row className="mb-2">
                        <ResidualRiskRatingSection
                          riskParameters={sortedRiskParameterListForRiskRaiting}
                          job={job}
                          jobIndex={idx}
                          onRiskRatingClick={handleRiskRatingClick}
                          onReasonChange={handleReasonChange}
                          jobErrors={jobErrors[idx] || {}}
                          reasonErrors={reasonErrors[idx] || {}} // pass only this job's errors
                          setReasonErrors={errObj =>
                            setReasonErrors(prev => ({
                              ...prev,
                              [idx]: errObj,
                            }))
                          }
                          touched={touched[idx] || {}}
                          type={type}
                        />
                      </Row>
                      {type === 'risk' && (
                        <CloseOutSection
                          job={job}
                          jobIndex={idx}
                          onJobChange={handleJobChange}
                          ranks={ranks}
                          jobErrors={jobErrors[idx] || {}}
                          touched={touched[idx] || {}}
                          setTouched={setTouched}
                        />
                      )}
                    </Card.Body>
                  </div>
                </Collapse>
              </Card>
            ),
          )}
        </div>

        {/* Modal for Initial/Residual Risk Rating */}
        <InitialRiskRatingModal
          show={showModal}
          onHide={() => setShowModal(false)}
          onSelect={value => {
            handleModalSelection(value, selectedJobIdx, selectedRiskCategory, {
              modalType,
              form,
              handleJobChange,
              setJobErrors,
              validateAndNotify,
              setTouched,
              type,
            });
            setShowModal(false);
          }}
          selectedValue={getSelectedModalValue(
            selectedJobIdx,
            selectedRiskCategory,
            modalType,
            form,
            type,
          )}
          irrValue={
            modalType === 'residual' &&
            selectedJobIdx !== null &&
            selectedRiskCategory !== null
              ? (() => {
                  const jobsArray =
                    type === 'risk'
                      ? (form as RiskForm)?.risk_job
                      : (form as TemplateForm)?.template_job;
                  const job = jobsArray?.[selectedJobIdx];
                  if (!job) return '';

                  const initialRatings =
                    type === 'risk'
                      ? (job as RiskFormJob).risk_job_initial_risk_rating
                      : (job as TemplateFormJob)
                          .template_job_initial_risk_rating;

                  return (
                    initialRatings?.find?.(
                      (r: any) => r?.parameter_type_id === selectedRiskCategory,
                    )?.rating || ''
                  );
                })()
              : ''
          }
          title={`${
            modalType === 'initial'
              ? 'Initial Risk Rating'
              : 'Residual Risk Rating'
          } - ${
            sortedRiskParameterListForRiskRaiting?.find?.(
              p => p.id === selectedRiskCategory,
            )?.name || ''
          }`}
        />
      </div>
    );
  },
);

AddJobsStep.displayName = 'AddJobsStep';
