import React, {useCallback, useState} from 'react';
import {Row, Button, Dropdown, OverlayTrigger, Tooltip} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import type {ColumnDef} from '@tanstack/react-table';
import InfiniteScrollTable from '../../components/InfiniteScrollTable';
import {
  FilterValue,
  RAFilters,
  raFiltersInitialState,
  RAFilterValues,
} from './components/RAFilters';
import {useInfiniteQuery} from '../../hooks';
import {getRAList} from '../../services/services';
import {
  assessorLabel,
  cleanObject,
  getDateRangeFilters,
  getRaApprovalStatus,
  getRiskRatingStatus,
  parseDate,
  raLevelColor,
  raLevelLabel,
  withDefault,
} from '../../utils/common';
import {RiskListResponse} from '../../types';
import TruncateText from '../../components/TruncateBasicText';
import Link from '../../components/Link';
import ColoredTile from '../../components/ColoredTile';

import '../../styles/components/ra-listing.scss';
import RiskApproverCommentsTooltip from './components/RiskApproverCommentsTooltip';

export default function RAListing() {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<RAFilterValues>(raFiltersInitialState);
  const [sorting, setSorting] = useState<Array<{id: string; desc: boolean}>>([
    {id: 'publish_on', desc: true},
  ]);

  let sort_order = undefined;
  if (typeof sorting[0]?.desc === 'boolean') {
    sort_order = sorting[0].desc ? 'DESC' : 'ASC';
  }

  const filtersAndSorters = {
    sort_by: sorting[0]?.id,
    sort_order,
    search: filters.search || null,
    status: filters.approval_status?.[0] || null,
    vessel_id: filters.vessel_or_office?.vessel_id || null,
    office_id: filters.vessel_or_office?.office_id || null,
    vessel_category: filters.vessel_category || null,
    ra_level: filters.ra_level?.[0] || null,
    ...getDateRangeFilters('submitted_on', filters.submitted_on),
    ...getDateRangeFilters('approval_date', filters.approval_date),
    ...getDateRangeFilters('assessment_date', filters.assessment_date),
  };

  const {data, isFetchingNextPage, isLoading, fetchNextPage} = useInfiniteQuery<
    RiskListResponse['result']['data'][0],
    RiskListResponse['result']
  >(getRAList, {
    limit: 100,
    ...cleanObject(filtersAndSorters),
  });

  const handleFilterChange = useCallback(
    (key: keyof RAFilterValues, value: FilterValue) => {
      setFilters(prev => ({...prev, [key]: value}));
    },
    [],
  );

  return (
    <div className="ra-listing-page">
      <Row className="mb-3">
        <div
          className="d-flex"
          style={{
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            marginLeft: 15,
            marginRight: 15,
          }}
        >
          <div>
            <h3 className="breadcrumb-header">Risk Assessment</h3>
          </div>
          <div className="d-flex" style={{gap: 12}}>
            <Button
              variant="outline-primary"
              className="me-2 basic-btn"
              onClick={() => navigate('/risk-assessment/template-listing')}
            >
              View Templates
            </Button>
            <Button
              variant="outline-primary"
              className="me-2 basic-btn"
              onClick={() => navigate('/risk-assessment/drafts')}
            >
              Drafts
            </Button>
            <Dropdown>
              <Dropdown.Toggle
                className="basic-btn create-new-btn"
                variant="primary"
              >
                Create New
              </Dropdown.Toggle>
              <Dropdown.Menu>
                <Dropdown.Item
                  onClick={() =>
                    navigate('/risk-assessment/template-selection')
                  }
                >
                  Create RA using Template
                </Dropdown.Item>
                <Dropdown.Item
                  onClick={() => {
                    navigate('/risk-assessment/risks/create');
                  }}
                >
                  Create RA without a Template
                </Dropdown.Item>
                <Dropdown.Item
                  onClick={() => navigate('/risk-assessment/templates/create')}
                >
                  Create RA Template
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </div>
      </Row>

      <RAFilters filters={filters} onFilterChange={handleFilterChange} />

      <InfiniteScrollTable
        columns={columns}
        data={data.data}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        pagination={data.pagination}
        onRowClick={row => {
          // Don't navigate if clicking on an anchor tag
          // using NOSONAR to ignore the warning as using MouseEvent is breaking the functionality
          const target = window.event?.target as HTMLElement; // NOSONAR
          if (!target?.closest('a')) {
            navigate(`/risk-assessment/approval/${row.id}`);
          }
        }}
        sorting={{
          sorting,
          onSortingChange: value => {
            if (value.length === 0) {
              setSorting([{id: 'created_at', desc: true}]);
            } else {
              setSorting(value);
            }
          },
        }}
      />
    </div>
  );
}

const columns: ColumnDef<RiskListResponse['result']['data'][0]>[] = [
  {
    accessorKey: 'task_requiring_ra',
    header: 'Task Required',
    cell: info => {
      const value = info.getValue();
      return <TruncateText text={withDefault(String(value))} maxLength={56} />;
    },
    meta: {isSticky: true, stickySide: 'left'},
    minSize: 420,
  },
  {
    header: 'Vessel/Office Name',
    cell: ({row}) => {
      const value = row.original.office_name || row.original.vessel_name;
      const displayText =
        value && value.length > 24
          ? `${value.slice(0, 21)}...`
          : withDefault(value);

      const content = (
        <OverlayTrigger
          placement="top"
          overlay={<Tooltip id={`tooltip-${row.id}`}>{value}</Tooltip>}
        >
          <span
            className="text-truncate d-inline-block"
            style={{maxWidth: 220}}
          >
            {displayText}
          </span>
        </OverlayTrigger>
      );

      return row.original.vessel_name ? (
        <Link
          href={`/vessel/ownership/details/${row.original.vessel_ownership_id}`}
        >
          {content}
        </Link>
      ) : (
        content
      );
    },
    enableSorting: false,
    minSize: 250,
  },
  {
    accessorKey: 'vessel_category',
    header: 'Vessel Category',
    cell: info => withDefault(info.getValue() as string),
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'assessor',
    header: 'Assessor',
    cell: info => {
      const value = info.getValue() as number;
      return withDefault(assessorLabel[value]);
    },
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'publish_on',
    header: 'Submitted on',
    cell: info => withDefault(parseDate(info.getValue() as string)),
    minSize: 160,
  },
  {
    accessorKey: 'vessel_tech_group',
    header: 'Tech Group',
    cell: info => withDefault(info.getValue() as string),
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'ra_level',
    header: 'Level of RA',
    cell: info => {
      const label = raLevelLabel[info.getValue() as number] ?? 'Unassigned';
      const theme = raLevelColor[info.getValue() as number] ?? 'blue-2';
      return <ColoredTile text={label} theme={theme} />;
    },
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'approval_date',
    header: 'Approval Date',
    cell: info => {
      return withDefault(parseDate(info?.row?.original?.approval_date));
    },
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'status',
    header: 'Approval Status',
    cell: info => {
      const [statusText, theme] = getRaApprovalStatus(info.row.original.status);
      return <ColoredTile text={statusText} theme={theme} />;
    },
    enableSorting: false,
    minSize: 180,
  },
  {
    accessorKey: 'risk_rating',
    header: 'Overall Risk Rating',
    cell: info => {
      const [statusText, theme] = getRiskRatingStatus(
        info.row.original.risk_rating,
      );
      if (!statusText || !theme) return <span>---</span>;
      return <ColoredTile text={statusText} theme={theme} />;
    },
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'date_risk_assessment',
    header: 'Date of Risk Assessment',
    cell: info => withDefault(parseDate(info.getValue() as string)),
    minSize: 210,
  },
  {
    accessorKey: 'status',
    header: 'Comments',
    meta: {isSticky: true, stickySide: 'right'},
    cell: info => (
      <RiskApproverCommentsTooltip
        riskApprovers={info?.row?.original?.risk_approver || []}
      />
    ),
    enableSorting: false,
    minSize: 160,
  },
  {
    accessorKey: 'id',
    header: 'Action',
    meta: {isSticky: true, stickySide: 'right'},
    cell: info => (
      <a
        href={`/risk-assessment/approval/${String(info.getValue())}`}
        className="view-btn"
      >
        View
      </a>
    ),
    enableSorting: false,
  },
];

export {columns};
