import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {InputComponent} from '../../src/components/InputComponent';

describe('InputComponent', () => {
  const mockOnChange = jest.fn();
  const mockOnBlur = jest.fn();

  const defaultProps = {
    form: {testInput: 'initial value'},
    label: 'Test Label',
    name: 'testInput',
    onChange: mockOnChange,
    onBlur: mockOnBlur,
    placeholder: 'Enter text',
    maxLength: 10,
    helpText: 'Helpful text',
    error: '',
    showMaxLength: false,
    value: 'initial value',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders label and input with correct props', () => {
    render(<InputComponent {...defaultProps} />);

    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();

    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveValue('initial value');
    expect(input).toHaveAttribute('placeholder', 'Enter text');
    expect(input).not.toBeDisabled();
    expect(input).toHaveAttribute('name', 'testInput');
    expect(input).toHaveAttribute('maxLength', '10');
  });

  it('renders a textarea when type is textarea', () => {
    render(<InputComponent {...defaultProps} type="textarea" rows={5} />);
    const textarea = screen.getByLabelText('Test Label');
    expect(textarea.tagName).toBe('TEXTAREA');
    expect(textarea).toHaveAttribute('rows', '5');
  });

  it('calls onChange and onBlur handlers', () => {
    render(<InputComponent {...defaultProps} />);
    const input = screen.getByLabelText('Test Label');

    fireEvent.change(input, {target: {value: 'changed value'}});
    expect(mockOnChange).toHaveBeenCalledTimes(1);

    fireEvent.blur(input);
    expect(mockOnBlur).toHaveBeenCalledTimes(1);
  });

  it('displays helpText and error messages correctly', () => {
    const errorProps = {
      ...defaultProps,
      error: 'Error message',
      helpText: 'Helpful text',
    };
    render(<InputComponent {...errorProps} />);

    expect(screen.getByText('Helpful text')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
  });

  it('applies disabled attribute', () => {
    render(<InputComponent {...defaultProps} disabled />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toBeDisabled();
  });

  it('renders max length counter when showMaxLength is true', () => {
    render(<InputComponent {...defaultProps} showMaxLength />);

    expect(
      screen.getByText('13/10') ||
        screen.queryByText('initial value'.length + '/10'),
    ).toBeInTheDocument();
  });

  it('applies custom className and style', () => {
    const style = {backgroundColor: 'red'};
    render(
      <InputComponent
        {...defaultProps}
        className="custom-class"
        style={style}
      />,
    );

    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveClass('custom-class');
    expect(input).toHaveStyle('background-color: red');
  });

  it('uses custom classes from classes prop', () => {
    const classes = {
      group: 'group-class',
      label: 'label-class',
      component: 'component-class',
    };
    render(<InputComponent {...defaultProps} classes={classes} />);

    expect(screen.getByText('Test Label')).toHaveClass('label-class');

    const group =
      screen.getByText('Test Label').closest('.mb-3') ||
      screen.getByText('Test Label').parentElement;
    expect(group).toHaveClass('group-class');

    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveClass('component-class');
  });

  // Additional tests for 100% coverage
  it('renders with different input types', () => {
    // Test each type individually to ensure proper rendering
    render(<InputComponent {...defaultProps} type="text" />);
    let input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();

    // Test number type
    const {unmount: unmountNumber} = render(
      <InputComponent {...defaultProps} type="number" />
    );
    input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();
    unmountNumber();

    // Test email type
    const {unmount: unmountEmail} = render(
      <InputComponent {...defaultProps} type="email" />
    );
    input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();
    unmountEmail();

    // Test password type
    const {unmount: unmountPassword} = render(
      <InputComponent {...defaultProps} type="password" />
    );
    input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();
    unmountPassword();
  });

  it('handles empty form object', () => {
    render(<InputComponent {...defaultProps} form={{}} value="" />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveValue('');
  });

  it('handles null form object', () => {
    render(<InputComponent {...defaultProps} form={null as any} value="" />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveValue('');
  });

  it('handles undefined form object', () => {
    render(<InputComponent {...defaultProps} form={undefined} value="" />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveValue('');
  });

  it('renders with custom formControlId', () => {
    render(
      <InputComponent {...defaultProps} formControlId="custom-control-id" />
    );
    const formGroup = screen.getByLabelText('Test Label').closest('[id="custom-control-id"]');
    expect(formGroup).toBeInTheDocument();
  });

  it('uses name as controlId when formControlId is null', () => {
    render(<InputComponent {...defaultProps} formControlId={undefined} />);
    const formGroup = screen.getByLabelText('Test Label').closest('[id="testInput"]');
    expect(formGroup).toBeInTheDocument();
  });

  it('renders without onBlur handler', () => {
    render(<InputComponent {...defaultProps} onBlur={undefined} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();
    // Should not throw error when blurring
    fireEvent.blur(input);
  });

  it('handles maxLength as undefined', () => {
    render(<InputComponent {...defaultProps} maxLength={undefined} />);
    const input = screen.getByLabelText('Test Label');
    // When maxLength is undefined, it should fall back to the default value of 255
    expect(input).toHaveAttribute('maxLength', '255');
  });

  it('handles maxLength as null', () => {
    render(<InputComponent {...defaultProps} maxLength={undefined} />);
    const input = screen.getByLabelText('Test Label');
    // When maxLength is undefined, it should fall back to the default value of 255
    expect(input).toHaveAttribute('maxLength', '255');
  });

  it('renders with empty helpText', () => {
    render(<InputComponent {...defaultProps} helpText="" />);
    // Should still render the Form.Text element but with empty content
    const helpTextElements = screen.getAllByText('');
    expect(helpTextElements.length).toBeGreaterThan(0);
  });

  it('renders with null helpText', () => {
    render(<InputComponent {...defaultProps} helpText={undefined} />);
    const helpTextElements = screen.getAllByText('');
    expect(helpTextElements.length).toBeGreaterThan(0);
  });

  it('renders with undefined helpText', () => {
    render(<InputComponent {...defaultProps} helpText={undefined} />);
    const helpTextElements = screen.getAllByText('');
    expect(helpTextElements.length).toBeGreaterThan(0);
  });

  it('renders with empty error', () => {
    render(<InputComponent {...defaultProps} error="" />);
    const input = screen.getByLabelText('Test Label');
    expect(input).not.toHaveClass('is-invalid');
  });

  it('renders with null error', () => {
    render(<InputComponent {...defaultProps} error={undefined} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).not.toHaveClass('is-invalid');
  });

  it('renders with undefined error', () => {
    render(<InputComponent {...defaultProps} error={undefined} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).not.toHaveClass('is-invalid');
  });

  it('applies isInvalid when error is truthy', () => {
    render(<InputComponent {...defaultProps} error="Some error" />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveClass('is-invalid');
  });

  it('renders max length counter with empty form value', () => {
    render(
      <InputComponent
        {...defaultProps}
        form={{}}
        value=""
        showMaxLength
        maxLength={50}
      />
    );
    expect(screen.getByText('0/50')).toBeInTheDocument();
  });

  it('renders max length counter with null form value', () => {
    render(
      <InputComponent
        {...defaultProps}
        form={{testInput: null}}
        value=""
        showMaxLength
        maxLength={50}
      />
    );
    expect(screen.getByText('0/50')).toBeInTheDocument();
  });

  it('renders max length counter with undefined form value', () => {
    render(
      <InputComponent
        {...defaultProps}
        form={{testInput: undefined}}
        value=""
        showMaxLength
        maxLength={50}
      />
    );
    expect(screen.getByText('0/50')).toBeInTheDocument();
  });

  it('renders without classes prop', () => {
    render(<InputComponent {...defaultProps} classes={undefined} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();

    const formGroup = input.closest('.mb-3');
    expect(formGroup).toHaveClass('mb-3');
  });

  it('renders with partial classes object', () => {
    const partialClasses = {
      group: 'custom-group',
      // label and component are undefined
    };
    render(<InputComponent {...defaultProps} classes={partialClasses} />);

    const input = screen.getByLabelText('Test Label');
    const formGroup = input.closest('.custom-group');
    expect(formGroup).toBeInTheDocument();
  });

  it('combines custom className with classes.component', () => {
    const classes = {
      component: 'component-class',
    };
    render(
      <InputComponent
        {...defaultProps}
        classes={classes}
        className="additional-class"
      />
    );

    const input = screen.getByLabelText('Test Label');
    expect(input).toHaveClass('component-class');
    expect(input).toHaveClass('additional-class');
  });

  it('handles textarea with default rows when rows prop is not provided', () => {
    render(<InputComponent {...defaultProps} type="textarea" />);
    const textarea = screen.getByLabelText('Test Label');
    expect(textarea.tagName).toBe('TEXTAREA');
    expect(textarea).toHaveAttribute('rows', '3'); // default value
  });

  it('renders textarea with custom rows', () => {
    render(<InputComponent {...defaultProps} type="textarea" rows={8} />);
    const textarea = screen.getByLabelText('Test Label');
    expect(textarea).toHaveAttribute('rows', '8');
  });

  it('does not apply rows attribute to non-textarea inputs', () => {
    render(<InputComponent {...defaultProps} type="text" rows={5} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).not.toHaveAttribute('rows');
  });

  // Tests for default parameter values to achieve 100% branch coverage
  it('uses default values when props are not provided', () => {
    const minimalProps = {
      form: {},
      onChange: mockOnChange,
      value: '',
      label: '',
      name: '',
    };

    render(<InputComponent {...minimalProps} />);

    // Should use default label (empty string)
    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();

    // Should use default placeholder (empty string)
    expect(input).toHaveAttribute('placeholder', '');

    // Should use default name (empty string)
    expect(input).toHaveAttribute('name', '');

    // Should use default maxLength (255)
    expect(input).toHaveAttribute('maxLength', '255');

    // Should use default showMaxLength (false) - no counter should be visible
    expect(screen.queryByText(/\/255/)).not.toBeInTheDocument();
  });

  it('uses default className when not provided', () => {
    const propsWithoutClassName = {
      ...defaultProps,
      className: undefined,
    };

    render(<InputComponent {...propsWithoutClassName} />);
    const input = screen.getByLabelText('Test Label');

    // Should not have any additional classes beyond the default ones
    expect(input).toBeInTheDocument();
  });

  it('uses default style when not provided', () => {
    const propsWithoutStyle = {
      ...defaultProps,
      style: undefined,
    };

    render(<InputComponent {...propsWithoutStyle} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).toBeInTheDocument();
  });

  it('uses default disabled state when not provided', () => {
    const propsWithoutDisabled = {
      ...defaultProps,
      disabled: undefined,
    };

    render(<InputComponent {...propsWithoutDisabled} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).not.toBeDisabled();
  });

  it('uses default type when not provided', () => {
    const propsWithoutType = {
      ...defaultProps,
      type: undefined,
    };

    render(<InputComponent {...propsWithoutType} />);
    const input = screen.getByLabelText('Test Label');
    // Default type is 'text', which doesn't show as an attribute
    expect(input).not.toHaveAttribute('type');
  });

  it('uses default helpText when not provided', () => {
    const propsWithoutHelpText = {
      ...defaultProps,
      helpText: undefined,
    };

    render(<InputComponent {...propsWithoutHelpText} />);
    // Should render empty Form.Text
    const helpTextElements = screen.getAllByText('');
    expect(helpTextElements.length).toBeGreaterThan(0);
  });

  it('uses default error when not provided', () => {
    const propsWithoutError = {
      ...defaultProps,
      error: undefined,
    };

    render(<InputComponent {...propsWithoutError} />);
    const input = screen.getByLabelText('Test Label');
    expect(input).not.toHaveClass('is-invalid');
  });
});
