<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.dragdrop.test.tsx">
        <testCase name="AddJobsStep Drag and Drop should render drag handles when there are multiple jobs" duration="24">
            <failure message="Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports."><![CDATA[Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28478:17)
    at createFiberFromElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28504:15)
    at reconcileSingleElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:13986:23)
    at reconcileChildFibers (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:14044:35)
    at reconcileChildren (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19193:28)
    at updateHostRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19912:5)
    at beginWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21654:14)
    at beginWork$1 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at performConcurrentWorkOnRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
    at flushActQueue (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at Object.<anonymous> (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.dragdrop.test.tsx:113:11)
    at Promise.then.completed (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="AddJobsStep Drag and Drop should not render drag handles when there is only one job" duration="5">
            <failure message="Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports."><![CDATA[Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28478:17)
    at createFiberFromElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28504:15)
    at reconcileSingleElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:13986:23)
    at reconcileChildFibers (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:14044:35)
    at reconcileChildren (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19193:28)
    at updateHostRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19912:5)
    at beginWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21654:14)
    at beginWork$1 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at performConcurrentWorkOnRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
    at flushActQueue (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at Object.<anonymous> (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.dragdrop.test.tsx:132:11)
    at Promise.then.completed (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="AddJobsStep Drag and Drop should render correct number of job cards" duration="4">
            <failure message="Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports."><![CDATA[Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28478:17)
    at createFiberFromElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28504:15)
    at reconcileSingleElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:13986:23)
    at reconcileChildFibers (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:14044:35)
    at reconcileChildren (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19193:28)
    at updateHostRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19912:5)
    at beginWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21654:14)
    at beginWork$1 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at performConcurrentWorkOnRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
    at flushActQueue (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at Object.<anonymous> (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.dragdrop.test.tsx:150:11)
    at Promise.then.completed (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="AddJobsStep Drag and Drop should work with risk forms" duration="3">
            <failure message="Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports."><![CDATA[Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28478:17)
    at createFiberFromElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28504:15)
    at reconcileSingleElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:13986:23)
    at reconcileChildFibers (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:14044:35)
    at reconcileChildren (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19193:28)
    at updateHostRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19912:5)
    at beginWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21654:14)
    at beginWork$1 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at performConcurrentWorkOnRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
    at flushActQueue (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at Object.<anonymous> (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.dragdrop.test.tsx:170:11)
    at Promise.then.completed (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="AddJobsStep Drag and Drop should not render drag handles in edit mode" duration="4">
            <failure message="Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports."><![CDATA[Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28478:17)
    at createFiberFromElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:28504:15)
    at reconcileSingleElement (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:13986:23)
    at reconcileChildFibers (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:14044:35)
    at reconcileChildren (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19193:28)
    at updateHostRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:19912:5)
    at beginWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21654:14)
    at beginWork$1 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at performConcurrentWorkOnRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
    at flushActQueue (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at Object.<anonymous> (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.dragdrop.test.tsx:189:11)
    at Promise.then.completed (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
    </file>
</testExecutions>