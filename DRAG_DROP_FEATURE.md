# Drag and Drop Feature for Job Reordering in AddJobsStep

## Overview
This document describes the implementation of drag-and-drop functionality in the `AddJobsStep.tsx` component, allowing users to reorder jobs by dragging them when there are multiple jobs.

## Implementation Details

### Libraries Used
- `@dnd-kit/core` - Core drag and drop functionality
- `@dnd-kit/sortable` - Sortable list implementation
- `@dnd-kit/utilities` - Utility functions for transforms

### Key Components

#### 1. SortableJobCard Component
A new component that wraps each job card with drag-and-drop functionality:
- Uses `useSortable` hook from `@dnd-kit/sortable`
- Handles drag state and visual feedback
- Maintains all existing job card functionality

#### 2. Drag Handle
- Visual drag handle (⋮⋮) appears only when there are multiple jobs
- Located next to the job number in the card header
- Hover effects for better user experience
- Only visible in non-edit mode

#### 3. DndContext and SortableContext
- `DndContext` wraps the entire job list
- `SortableContext` manages the sortable items
- Uses `verticalListSortingStrategy` for vertical list sorting

### Features

#### When Drag and Drop is Available
- Multiple jobs exist (2 or more)
- Not in edit mode (`isEdit={false}`)
- Drag handles are visible and functional

#### When Drag and Drop is Disabled
- Single job only
- Edit mode is active
- Drag handles are hidden

#### Drag Behavior
- **Activation**: 8px distance threshold to prevent accidental drags
- **Visual Feedback**: Dragged item becomes semi-transparent (50% opacity)
- **Keyboard Support**: Full keyboard navigation support
- **Accessibility**: Proper ARIA attributes and screen reader support

### State Management

#### Job Reordering
When a job is moved from one position to another:
1. The jobs array is reordered using `arrayMove` utility
2. Form state is updated with the new job order
3. All related state objects are reordered:
   - `expanded` - tracks which job cards are expanded
   - `touched` - tracks which fields have been interacted with
   - `jobErrors` - tracks validation errors per job
   - `reasonErrors` - tracks reason validation errors per job

#### State Preservation
- Expanded state follows the moved job
- Validation states are preserved
- Form data integrity is maintained

### Technical Implementation

#### Sensors Configuration
```typescript
const sensors = useSensors(
  useSensor(PointerSensor, {
    activationConstraint: {
      distance: 8, // Prevents accidental drags
    },
  }),
  useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  })
);
```

#### Drag End Handler
```typescript
const handleDragEnd = (event: DragEndEvent) => {
  const {active, over} = event;
  
  if (active.id !== over?.id) {
    // Find old and new indices
    // Reorder jobs array
    // Update form state
    // Reorder related state objects
  }
};
```

### User Experience

#### Visual Indicators
- Drag handle appears on hover with background color change
- Cursor changes to "grab" when hovering over drag handle
- Dragged item becomes semi-transparent during drag
- Smooth animations for reordering

#### Accessibility
- Keyboard navigation support
- Screen reader compatible
- Proper focus management
- ARIA attributes for drag and drop actions

### Browser Compatibility
- Modern browsers with ES6+ support
- Touch device support for mobile/tablet
- Keyboard-only navigation support

### Performance Considerations
- Efficient state updates using React's batching
- Minimal re-renders during drag operations
- Optimized for large job lists

## Usage

The drag-and-drop functionality is automatically enabled when:
1. There are 2 or more jobs in the form
2. The component is not in edit mode
3. The user has appropriate permissions

No additional configuration is required - the feature works out of the box with existing job management functionality.

## Testing

The implementation includes:
- Type safety with TypeScript
- Proper error handling
- State consistency validation
- Integration with existing validation logic

## Future Enhancements

Potential improvements could include:
- Visual drop zones
- Drag preview customization
- Batch job reordering
- Undo/redo functionality
- Animation improvements
